from app.extensions import db
from flask_login import UserMixin

class User(db.Model, UserMixin):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # 'superadmin', 'admin', 'customer'
    is_active = db.Column(db.<PERSON><PERSON>, default=True)

    def __repr__(self):
        return f'<User {self.email}>'