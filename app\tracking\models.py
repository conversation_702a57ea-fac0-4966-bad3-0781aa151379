from app.extensions import db
from datetime import datetime

class Vehicle(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    license_plate = db.Column(db.String(20), unique=True)
    is_active = db.Column(db.Boolean, default=True)
    last_updated = db.Column(db.DateTime)
    locations = db.relationship('Location', backref='vehicle', lazy=True)

class Location(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    vehicle_id = db.Column(db.Integer, db.<PERSON>ey('vehicle.id'))
    lat = db.Column(db.Float, nullable=False)
    lng = db.Column(db.Float, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    speed = db.Column(db.Float)

class Geofence(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50))
    coordinates = db.Column(db.JSON)
    is_active = db.Column(db.<PERSON>olean, default=True)