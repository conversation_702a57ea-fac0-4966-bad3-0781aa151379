function initMap(vehicles) {
    const map = L.map('map').setView([0, 0], 2);
    <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
    
    vehicles.forEach(vehicle => {
        if (vehicle.last_location) {
            L.marker([vehicle.last_location.lat, vehicle.last_location.lng])
                .bindPopup(`<b>${vehicle.name}</b><br>${vehicle.license_plate}`)
                .addTo(map);
        }
    });
}