from shapely.geometry import Point, Polygon
from app.tracking.models import Geofence
from app.auth.models import User
from flask import current_app

def check_geofence(lat, lng, vehicle_id):
    point = Point(lng, lat)
    geofences = Geofence.query.filter_by(is_active=True).all()
    
    for fence in geofences:
        polygon = Polygon(fence.coordinates)
        if not polygon.contains(point):
            trigger_alert(
                vehicle_id=vehicle_id,
                message=f"Left geofence: {fence.name}"
            )

def trigger_alert(vehicle_id, message):
    # Send to admins (e.g., email, WebSocket, or SMS)
    admins = User.query.filter_by(role='admin').all()
    for admin in admins:
        current_app.logger.info(f"ALERT to {admin.email}: {message}")
    # Future: Integrate with Celery for async alerts