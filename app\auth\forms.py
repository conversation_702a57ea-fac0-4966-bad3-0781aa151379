from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, Password<PERSON>ield, SelectField
from wtforms.validators import DataRequired, Email

class CreateUserForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired()])
    role = SelectField('Role', choices=[
        ('admin', 'Admin'),
        ('customer', 'Customer')  # Superadmin can't be assigned via form
    ], validators=[DataRequired()])