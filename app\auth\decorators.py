from functools import wraps
from flask import abort, current_app
from flask_login import current_user

def role_required(role_name):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated or current_user.role != role_name:
                abort(403)  # Forbidden
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Shortcut decorators
superadmin_required = role_required('superadmin')
admin_required = role_required('admin')